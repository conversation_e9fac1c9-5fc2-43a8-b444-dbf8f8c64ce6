# MCP Copilot 项目结构

本项目已按照标准苹果生态项目结构进行重构，采用分层设计、模块化架构和现代 SwiftUI 开发模式。

## 📁 项目结构

```
mcpcopilot/
├── App/                           # 应用层
│   ├── DIContainer.swift          # 依赖注入容器
│   └── MCPCopilotApp.swift        # 主应用入口
├── Data/                          # 数据层
│   ├── Models/
│   │   └── DataModels.swift       # 数据模型定义
│   └── Repository/
│       ├── SceneRepository.swift  # 场景数据仓库
│       └── ConfigurationRepository.swift # 配置数据仓库
├── Modules/                       # 功能模块层
│   ├── Home/                      # 主页模块
│   │   ├── View/
│   │   │   └── HomeView.swift
│   │   └── ViewModel/
│   │       └── HomeViewModel.swift
│   ├── Settings/                  # 设置模块
│   │   ├── View/
│   │   │   └── SettingsView.swift
│   │   └── ViewModel/
│   │       └── SettingsViewModel.swift
│   ├── Monitor/                   # 监控模块
│   │   ├── View/
│   │   │   └── MonitorView.swift
│   │   └── ViewModel/
│   │       └── MonitorViewModel.swift
│   └── Configuration/             # 配置模块（待实现）
├── Resources/                     # 资源文件
│   ├── Assets.xcassets/           # 图片资源
│   └── Localization/              # 国际化资源
│       ├── en.lproj/
│       │   └── Localizable.strings
│       └── zh-Hans.lproj/
│           └── Localizable.strings
└── Shared/                        # 共享层
    ├── Components/                # 共享组件
    │   ├── SceneRowView.swift
    │   ├── SceneDetailView.swift
    │   ├── SceneEditView.swift
    │   └── MenuBarView.swift
    ├── Resources/                 # 设计系统
    │   ├── Colors.swift           # 颜色系统
    │   ├── Spacing.swift          # 间距系统
    │   └── Typography.swift       # 字体系统
    ├── Services/                  # 服务层
    │   ├── Configuration/
    │   │   └── ConfigurationService.swift
    │   ├── Process/
    │   │   └── ProcessService.swift
    │   ├── Monitoring/
    │   │   └── MonitoringService.swift
    │   ├── Logging/
    │   │   └── LoggingService.swift
    │   ├── Storage/
    │   │   └── StorageService.swift
    │   └── ServiceProtocols.swift
    └── Utils/                     # 工具类（待实现）
```

## 🏗️ 架构设计

### 1. 分层架构
- **App层**: 应用启动和依赖注入管理
- **Data层**: 数据模型和数据访问逻辑
- **Modules层**: 按功能组织的业务模块
- **Shared层**: 跨模块共享的组件和服务

### 2. 依赖注入
- 使用 `DIContainer` 管理所有服务和 ViewModel 实例
- 通过 SwiftUI Environment 传递依赖
- 支持单例模式和生命周期管理

### 3. MVVM 模式
- 每个功能模块包含 View 和 ViewModel
- ViewModel 负责业务逻辑和状态管理
- View 专注于 UI 展示和用户交互

### 4. 服务层设计
- 基于协议的服务接口设计
- 支持异步操作和错误处理
- 服务间解耦，便于测试和维护

## 🎨 设计系统

### 颜色系统 (Colors.swift)
- 主色调：蓝色、绿色、红色、橙色、紫色
- 语义化颜色：成功、警告、错误、信息
- 支持深色模式适配
- 状态相关颜色：进程状态、日志级别

### 间距系统 (Spacing.swift)
- 基础间距单位：xs(4), sm(8), md(16), lg(24), xl(32)
- 组件特定间距：卡片、按钮、表单、列表等
- 响应式间距支持

### 字体系统 (Typography.swift)
- 系统字体和自定义字体
- 语义化字体：标题、正文、标签、代码等
- 支持不同字重和设计风格

## 🔧 核心功能

### 1. 场景管理
- 创建、编辑、删除 MCP 场景
- 场景启动、停止、重启控制
- 环境变量配置

### 2. 进程管理
- yamcp 和 supergateway 进程控制
- 进程状态监控和自动重启
- 进程输出日志收集

### 3. 系统监控
- 实时 CPU 和内存使用率监控
- 进程资源使用情况跟踪
- 可视化监控界面

### 4. 日志系统
- 多级别日志记录
- 日志过滤和搜索
- 日志导出功能

### 5. 配置管理
- 应用配置持久化
- 用户偏好设置
- 配置导入导出

## 🌐 国际化支持

- 支持中文简体和英文
- 完整的本地化字符串
- 可扩展的多语言架构

## 📱 用户界面

### 主界面 (HomeView)
- 侧边栏场景列表
- 场景详情展示
- 快速操作按钮

### 设置界面 (SettingsView)
- 通用设置：端口、自动启动、语言等
- 高级设置：日志级别、监控间隔等
- 关于信息

### 监控界面 (MonitorView)
- 系统资源监控图表
- 进程列表和状态
- 实时日志查看

### 菜单栏 (MenuBarView)
- 快速场景切换
- 系统托盘集成
- 常用操作快捷方式

## 🚀 下一步开发

1. **完善进程管理**: 实现 yamcp 和 supergateway 的实际集成
2. **监控图表**: 添加实时监控图表和历史数据
3. **配置导入导出**: 支持场景配置的导入导出
4. **插件系统**: 支持 MCP Server 插件市场
5. **云端同步**: 配置的云端备份和同步
6. **性能优化**: 内存使用优化和启动速度提升

## 🧪 测试建议

1. **单元测试**: 为 ViewModel 和 Service 编写单元测试
2. **集成测试**: 测试服务间的交互和数据流
3. **UI测试**: 使用 SwiftUI Preview 和 UI 测试框架
4. **性能测试**: 监控内存使用和 CPU 性能

这个重构后的项目结构提供了良好的可维护性、可扩展性和代码组织，符合现代 macOS 应用开发的最佳实践。
