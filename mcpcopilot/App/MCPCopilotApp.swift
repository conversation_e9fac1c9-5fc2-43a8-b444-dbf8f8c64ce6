//
//  MCPCopilotApp.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI
import SwiftData

@main
struct MCPCopilotApp: App {
    
    // MARK: - Properties
    @StateObject private var diContainer = DIContainer.shared
    
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            MCPScene.self,
            MCPServer.self,
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()

    // MARK: - Scene
    var body: some Scene {
        WindowGroup {
            HomeView()
                .environment(\.diContainer, diContainer)
                .modelContainer(sharedModelContainer)
                .onAppear {
                    setupApplication()
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowResizability(.contentSize)
        .defaultSize(width: 1200, height: 800)
        
        // 菜单栏
        MenuBarExtra("MCP Copilot", systemImage: "network") {
            MenuBarView()
                .environment(\.diContainer, diContainer)
        }
        .menuBarExtraStyle(.window)
    }
    
    // MARK: - Private Methods
    private func setupApplication() {
        // 应用启动时的初始化工作
        Task {
            await diContainer.loggingService.log("Application started", level: .info)
        }
    }
}
