//
//  Typography.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

/// 应用统一的字体系统
struct Typography {
    
    // MARK: - Font Sizes
    struct Size {
        static let caption: CGFloat = 11
        static let footnote: CGFloat = 13
        static let body: CGFloat = 15
        static let callout: CGFloat = 16
        static let subheadline: CGFloat = 17
        static let headline: CGFloat = 19
        static let title3: CGFloat = 21
        static let title2: CGFloat = 24
        static let title1: CGFloat = 28
        static let largeTitle: CGFloat = 34
    }
    
    // MARK: - Font Weights
    struct Weight {
        static let ultraLight = Font.Weight.ultraLight
        static let thin = Font.Weight.thin
        static let light = Font.Weight.light
        static let regular = Font.Weight.regular
        static let medium = Font.Weight.medium
        static let semibold = Font.Weight.semibold
        static let bold = Font.Weight.bold
        static let heavy = Font.Weight.heavy
        static let black = Font.Weight.black
    }
    
    // MARK: - System Fonts
    struct System {
        static let caption = Font.caption
        static let caption2 = Font.caption2
        static let footnote = Font.footnote
        static let body = Font.body
        static let callout = Font.callout
        static let subheadline = Font.subheadline
        static let headline = Font.headline
        static let title3 = Font.title3
        static let title2 = Font.title2
        static let title = Font.title
        static let largeTitle = Font.largeTitle
    }
    
    // MARK: - Custom Fonts
    struct Custom {
        // 标题字体
        static let pageTitle = Font.system(size: Size.title1, weight: Weight.bold, design: .default)
        static let sectionTitle = Font.system(size: Size.title3, weight: Weight.semibold, design: .default)
        static let cardTitle = Font.system(size: Size.headline, weight: Weight.medium, design: .default)
        
        // 正文字体
        static let bodyRegular = Font.system(size: Size.body, weight: Weight.regular, design: .default)
        static let bodyMedium = Font.system(size: Size.body, weight: Weight.medium, design: .default)
        static let bodySemibold = Font.system(size: Size.body, weight: Weight.semibold, design: .default)
        
        // 按钮字体
        static let buttonPrimary = Font.system(size: Size.callout, weight: Weight.medium, design: .default)
        static let buttonSecondary = Font.system(size: Size.callout, weight: Weight.regular, design: .default)
        
        // 标签字体
        static let label = Font.system(size: Size.subheadline, weight: Weight.medium, design: .default)
        static let caption = Font.system(size: Size.caption, weight: Weight.regular, design: .default)
        static let captionMedium = Font.system(size: Size.caption, weight: Weight.medium, design: .default)
        
        // 代码字体
        static let code = Font.system(size: Size.body, weight: Weight.regular, design: .monospaced)
        static let codeSmall = Font.system(size: Size.footnote, weight: Weight.regular, design: .monospaced)
        
        // 数字字体
        static let number = Font.system(size: Size.body, weight: Weight.medium, design: .monospaced)
        static let numberLarge = Font.system(size: Size.title3, weight: Weight.semibold, design: .monospaced)
    }
    
    // MARK: - Line Heights
    struct LineHeight {
        static let tight: CGFloat = 1.2
        static let normal: CGFloat = 1.4
        static let relaxed: CGFloat = 1.6
        static let loose: CGFloat = 1.8
    }
}

// MARK: - Font Extensions
extension Font {
    
    /// 创建自定义字体
    static func custom(size: CGFloat, weight: Font.Weight = .regular, design: Font.Design = .default) -> Font {
        return Font.system(size: size, weight: weight, design: design)
    }
    
    /// 根据上下文获取合适的字体
    static func contextual(for context: FontContext) -> Font {
        switch context {
        case .navigation:
            return Typography.Custom.label
        case .toolbar:
            return Typography.Custom.buttonSecondary
        case .statusBar:
            return Typography.Custom.caption
        case .logEntry:
            return Typography.Custom.code
        case .metric:
            return Typography.Custom.number
        case .error:
            return Typography.Custom.bodyMedium
        }
    }
}

// MARK: - Font Context
enum FontContext {
    case navigation
    case toolbar
    case statusBar
    case logEntry
    case metric
    case error
}

// MARK: - Text Style Extensions
extension Text {
    
    /// 应用页面标题样式
    func pageTitle() -> Text {
        self.font(Typography.Custom.pageTitle)
            .foregroundColor(.textPrimary)
    }
    
    /// 应用章节标题样式
    func sectionTitle() -> Text {
        self.font(Typography.Custom.sectionTitle)
            .foregroundColor(.textPrimary)
    }
    
    /// 应用卡片标题样式
    func cardTitle() -> Text {
        self.font(Typography.Custom.cardTitle)
            .foregroundColor(.textPrimary)
    }
    
    /// 应用正文样式
    func bodyText() -> Text {
        self.font(Typography.Custom.bodyRegular)
            .foregroundColor(.textPrimary)
    }
    
    /// 应用次要文本样式
    func secondaryText() -> Text {
        self.font(Typography.Custom.bodyRegular)
            .foregroundColor(.textSecondary)
    }
    
    /// 应用标签样式
    func labelText() -> Text {
        self.font(Typography.Custom.label)
            .foregroundColor(.textPrimary)
    }
    
    /// 应用说明文字样式
    func captionText() -> Text {
        self.font(Typography.Custom.caption)
            .foregroundColor(.textSecondary)
    }
    
    /// 应用代码样式
    func codeText() -> Text {
        self.font(Typography.Custom.code)
            .foregroundColor(.textPrimary)
    }
    
    /// 应用数字样式
    func numberText() -> Text {
        self.font(Typography.Custom.number)
            .foregroundColor(.textPrimary)
    }
    
    /// 应用错误文本样式
    func errorText() -> Text {
        self.font(Typography.Custom.bodyMedium)
            .foregroundColor(.statusError)
    }
    
    /// 应用成功文本样式
    func successText() -> Text {
        self.font(Typography.Custom.bodyMedium)
            .foregroundColor(.statusSuccess)
    }
    
    /// 应用警告文本样式
    func warningText() -> Text {
        self.font(Typography.Custom.bodyMedium)
            .foregroundColor(.statusWarning)
    }
}
