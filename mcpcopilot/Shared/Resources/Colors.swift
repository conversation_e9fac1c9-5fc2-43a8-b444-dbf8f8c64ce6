//
//  Colors.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

extension Color {
    
    // MARK: - Primary Colors
    static let primaryBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let primaryGreen = Color(red: 0.2, green: 0.78, blue: 0.35)
    static let primaryRed = Color(red: 1.0, green: 0.23, blue: 0.19)
    static let primaryOrange = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let primaryPurple = Color(red: 0.69, green: 0.32, blue: 0.87)
    
    // MARK: - Background Colors
    static let backgroundPrimary = Color(NSColor.controlBackgroundColor)
    static let backgroundSecondary = Color(NSColor.controlColor)
    static let backgroundTertiary = Color(NSColor.tertiarySystemFill)
    
    // MARK: - Text Colors
    static let textPrimary = Color(NSColor.labelColor)
    static let textSecondary = Color(NSColor.secondaryLabelColor)
    static let textTertiary = Color(NSColor.tertiaryLabelColor)
    static let textPlaceholder = Color(NSColor.placeholderTextColor)
    
    // MARK: - Status Colors
    static let statusSuccess = Color.primaryGreen
    static let statusWarning = Color.primaryOrange
    static let statusError = Color.primaryRed
    static let statusInfo = Color.primaryBlue
    
    // MARK: - Border Colors
    static let borderPrimary = Color(NSColor.separatorColor)
    static let borderSecondary = Color(NSColor.gridColor)
    
    // MARK: - Accent Colors
    static let accentBlue = Color.primaryBlue
    static let accentGreen = Color.primaryGreen
    static let accentRed = Color.primaryRed
    
    // MARK: - Process Status Colors
    static let processRunning = Color.primaryGreen
    static let processStopped = Color(NSColor.systemGray)
    static let processStarting = Color.primaryOrange
    static let processStopping = Color.primaryOrange
    static let processError = Color.primaryRed
    
    // MARK: - Chart Colors
    static let chartBlue = Color(red: 0.0, green: 0.48, blue: 1.0)
    static let chartGreen = Color(red: 0.2, green: 0.78, blue: 0.35)
    static let chartOrange = Color(red: 1.0, green: 0.58, blue: 0.0)
    static let chartRed = Color(red: 1.0, green: 0.23, blue: 0.19)
    static let chartPurple = Color(red: 0.69, green: 0.32, blue: 0.87)
    static let chartYellow = Color(red: 1.0, green: 0.8, blue: 0.0)
    
    // MARK: - Sidebar Colors
    static let sidebarBackground = Color(NSColor.controlBackgroundColor)
    static let sidebarSelection = Color(NSColor.selectedControlColor)
    
    // MARK: - Card Colors
    static let cardBackground = Color(NSColor.controlBackgroundColor)
    static let cardBorder = Color(NSColor.separatorColor)
    
    // MARK: - Button Colors
    static let buttonPrimary = Color.primaryBlue
    static let buttonSecondary = Color(NSColor.controlColor)
    static let buttonDanger = Color.primaryRed
    static let buttonSuccess = Color.primaryGreen
}

// MARK: - Color Scheme Support
extension Color {
    
    /// 根据当前颜色方案返回适配的颜色
    static func adaptive(light: Color, dark: Color) -> Color {
        return Color(NSColor.controlAccentColor)
    }
    
    /// 获取状态对应的颜色
    static func forProcessStatus(_ status: ProcessStatus) -> Color {
        switch status {
        case .running:
            return .processRunning
        case .stopped:
            return .processStopped
        case .starting:
            return .processStarting
        case .stopping:
            return .processStopping
        case .error:
            return .processError
        }
    }
    
    /// 获取日志级别对应的颜色
    static func forLogLevel(_ level: LogLevel) -> Color {
        switch level {
        case .debug:
            return .textSecondary
        case .info:
            return .statusInfo
        case .warning:
            return .statusWarning
        case .error:
            return .statusError
        }
    }
}

// MARK: - Gradient Colors
extension LinearGradient {
    
    static let primaryGradient = LinearGradient(
        colors: [Color.primaryBlue, Color.primaryPurple],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let successGradient = LinearGradient(
        colors: [Color.primaryGreen.opacity(0.8), Color.primaryGreen],
        startPoint: .top,
        endPoint: .bottom
    )
    
    static let warningGradient = LinearGradient(
        colors: [Color.primaryOrange.opacity(0.8), Color.primaryOrange],
        startPoint: .top,
        endPoint: .bottom
    )
    
    static let errorGradient = LinearGradient(
        colors: [Color.primaryRed.opacity(0.8), Color.primaryRed],
        startPoint: .top,
        endPoint: .bottom
    )
}
