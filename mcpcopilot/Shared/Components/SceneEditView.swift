//
//  SceneEditView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct SceneEditView: View {
    
    // MARK: - Properties
    let scene: MCPScene?
    let onSave: (MCPScene) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @Environment(\.diContainer) private var diContainer
    
    @State private var name: String = ""
    @State private var port: String = ""
    @State private var environment: [String: String] = [:]
    @State private var newEnvKey: String = ""
    @State private var newEnvValue: String = ""
    @State private var showingAddEnvironment = false
    
    @State private var errorMessage: String?
    
    private var isEditing: Bool {
        scene != nil
    }
    
    private var title: String {
        isEditing ? "编辑场景" : "创建场景"
    }
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            Form {
                basicInfoSection
                environmentSection
            }
            .formStyle(.grouped)
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    But<PERSON>("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("保存") {
                        saveScene()
                    }
                    .disabled(!isValidInput)
                }
            }
            .alert("错误", isPresented: .constant(errorMessage != nil)) {
                Button("确定") {
                    errorMessage = nil
                }
            } message: {
                if let errorMessage = errorMessage {
                    Text(errorMessage)
                }
            }
            .sheet(isPresented: $showingAddEnvironment) {
                addEnvironmentSheet
            }
        }
        .frame(minWidth: Spacing.Modal.minWidth, minHeight: Spacing.Modal.minHeight)
        .onAppear {
            loadSceneData()
        }
    }
    
    // MARK: - Basic Info Section
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("场景名称", text: $name)
                .textFieldStyle(.roundedBorder)
            
            TextField("端口号", text: $port)
                .textFieldStyle(.roundedBorder)
                .keyboardType(.numberPad)
        }
    }
    
    // MARK: - Environment Section
    private var environmentSection: some View {
        Section {
            if environment.isEmpty {
                Text("暂无环境变量")
                    .foregroundColor(.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ForEach(Array(environment.keys.sorted()), id: \.self) { key in
                    HStack {
                        Text(key)
                            .codeText()
                            .foregroundColor(.primaryBlue)
                        
                        Spacer()
                        
                        Text(environment[key] ?? "")
                            .codeText()
                            .lineLimit(1)
                            .truncationMode(.middle)
                        
                        Button(action: {
                            removeEnvironmentVariable(key)
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.statusError)
                        }
                        .buttonStyle(.borderless)
                    }
                }
            }
            
            Button("添加环境变量") {
                showingAddEnvironment = true
            }
            .buttonStyle(.bordered)
        } header: {
            Text("环境变量")
        }
    }
    
    // MARK: - Add Environment Sheet
    private var addEnvironmentSheet: some View {
        NavigationView {
            Form {
                Section("新环境变量") {
                    TextField("变量名", text: $newEnvKey)
                        .textFieldStyle(.roundedBorder)
                    
                    TextField("变量值", text: $newEnvValue)
                        .textFieldStyle(.roundedBorder)
                }
            }
            .formStyle(.grouped)
            .navigationTitle("添加环境变量")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("取消") {
                        showingAddEnvironment = false
                        clearNewEnvironmentFields()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("添加") {
                        addEnvironmentVariable()
                    }
                    .disabled(newEnvKey.isEmpty || newEnvValue.isEmpty)
                }
            }
        }
        .frame(width: 400, height: 300)
    }
    
    // MARK: - Computed Properties
    private var isValidInput: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !port.isEmpty &&
               Int(port) != nil &&
               Int(port)! > 0 &&
               Int(port)! <= 65535
    }
    
    // MARK: - Methods
    private func loadSceneData() {
        if let scene = scene {
            name = scene.name
            port = "\(scene.port)"
            environment = scene.environment
        } else {
            // 设置默认值
            let config = diContainer.configurationRepository.appConfiguration
            port = "\(config.defaultPort)"
        }
    }
    
    private func saveScene() {
        guard isValidInput else {
            errorMessage = "请检查输入的信息是否正确"
            return
        }
        
        guard let portNumber = Int(port) else {
            errorMessage = "端口号必须是有效的数字"
            return
        }
        
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        
        let savedScene: MCPScene
        if let existingScene = scene {
            // 编辑现有场景
            existingScene.name = trimmedName
            existingScene.port = portNumber
            existingScene.environment = environment
            existingScene.updatedAt = Date()
            savedScene = existingScene
        } else {
            // 创建新场景
            savedScene = MCPScene(
                name: trimmedName,
                port: portNumber,
                environment: environment
            )
        }
        
        onSave(savedScene)
        dismiss()
    }
    
    private func addEnvironmentVariable() {
        let key = newEnvKey.trimmingCharacters(in: .whitespacesAndNewlines)
        let value = newEnvValue.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !key.isEmpty && !value.isEmpty else { return }
        
        environment[key] = value
        showingAddEnvironment = false
        clearNewEnvironmentFields()
    }
    
    private func removeEnvironmentVariable(_ key: String) {
        environment.removeValue(forKey: key)
    }
    
    private func clearNewEnvironmentFields() {
        newEnvKey = ""
        newEnvValue = ""
    }
}

// MARK: - Preview
#Preview("Create Scene") {
    SceneEditView(scene: nil) { _ in }
}

#Preview("Edit Scene") {
    let scene = MCPScene(name: "开发环境", port: 7700)
    scene.environment = ["API_KEY": "sk-xxx", "DEBUG": "true"]
    
    return SceneEditView(scene: scene) { _ in }
}
