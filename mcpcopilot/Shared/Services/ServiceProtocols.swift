//
//  ServiceProtocols.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

// MARK: - Configuration Service Protocol
@MainActor
protocol ConfigurationServiceProtocol: ObservableObject {
    func initialize() async
    func loadConfiguration() async throws -> AppConfiguration
    func saveConfiguration(_ config: AppConfiguration) async throws
    func getDefaultConfiguration() -> AppConfiguration
}

// MARK: - Process Service Protocol
@MainActor
protocol ProcessServiceProtocol: ObservableObject {
    var runningProcesses: [String: Process] { get }
    
    func startScene(_ scene: MCPScene) async throws
    func stopScene(_ scene: MCPScene) async throws
    func restartScene(_ scene: MCPScene) async throws
    func getProcessStatus(for sceneId: UUID) -> ProcessStatus
    func killAllProcesses() async
}

// MARK: - Monitoring Service Protocol
@MainActor
protocol MonitoringServiceProtocol: ObservableObject {
    var systemResourceInfo: SystemResourceInfo { get }
    var processResourceInfos: [UUID: ProcessResourceInfo] { get }
    
    func startMonitoring()
    func stopMonitoring()
    func getProcessResourceInfo(for pid: Int32) -> ProcessResourceInfo?
}

// MARK: - Logging Service Protocol
@MainActor
protocol LoggingServiceProtocol: ObservableObject {
    var logEntries: [LogEntry] { get }
    
    func initialize() async
    func log(_ message: String, level: LogLevel, source: String?) async
    func clearLogs()
    func exportLogs() async throws -> URL
}

// MARK: - Storage Service Protocol
@MainActor
protocol StorageServiceProtocol: ObservableObject {
    func initialize() async
    func save<T: Codable>(_ object: T, to key: String) async throws
    func load<T: Codable>(_ type: T.Type, from key: String) async throws -> T?
    func delete(key: String) async throws
    func exists(key: String) -> Bool
}

// MARK: - Repository Protocols

@MainActor
protocol SceneRepositoryProtocol: ObservableObject {
    var scenes: [MCPScene] { get }
    
    func loadScenes() async throws
    func saveScene(_ scene: MCPScene) async throws
    func deleteScene(_ scene: MCPScene) async throws
    func getScene(by id: UUID) -> MCPScene?
}

@MainActor
protocol ConfigurationRepositoryProtocol: ObservableObject {
    var appConfiguration: AppConfiguration { get }
    
    func loadConfiguration() async throws
    func saveConfiguration(_ config: AppConfiguration) async throws
    func resetToDefault() async throws
}

// MARK: - App Configuration
struct AppConfiguration: Codable {
    var defaultPort: Int
    var autoStart: Bool
    var logLevel: LogLevel
    var maxRestartAttempts: Int
    var monitoringInterval: TimeInterval
    var enableSystemTray: Bool
    var language: String
    
    static let `default` = AppConfiguration(
        defaultPort: 7700,
        autoStart: false,
        logLevel: .info,
        maxRestartAttempts: 3,
        monitoringInterval: 5.0,
        enableSystemTray: true,
        language: "zh-Hans"
    )
}
