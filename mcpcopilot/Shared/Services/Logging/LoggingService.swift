//
//  LoggingService.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class LoggingService: LoggingServiceProtocol {
    
    // MARK: - Properties
    @Published private(set) var logEntries: [LogEntry] = []
    
    private let maxLogEntries = 1000
    private let logFileName = "app.log"
    private var logFileURL: URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        return documentsPath.appendingPathComponent("MCPCopilot/logs").appendingPathComponent(logFileName)
    }
    
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        return formatter
    }()
    
    // MARK: - Initialization
    func initialize() async {
        await createLogDirectoryIfNeeded()
        await loadExistingLogs()
        await log("Logging service initialized", level: .info, source: "LoggingService")
    }
    
    // MARK: - Logging Operations
    func log(_ message: String, level: LogLevel, source: String? = nil) async {
        let entry = LogEntry(
            timestamp: Date(),
            level: level,
            message: message,
            source: source
        )
        
        // 添加到内存中的日志条目
        logEntries.append(entry)
        
        // 限制内存中的日志条目数量
        if logEntries.count > maxLogEntries {
            logEntries.removeFirst(logEntries.count - maxLogEntries)
        }
        
        // 写入到文件
        await writeLogToFile(entry)
        
        // 控制台输出（调试模式）
        #if DEBUG
        print("[\(level.displayName)] \(dateFormatter.string(from: entry.timestamp)) \(source ?? "Unknown"): \(message)")
        #endif
    }
    
    func clearLogs() {
        logEntries.removeAll()
        
        // 清空日志文件
        Task {
            try? "".write(to: logFileURL, atomically: true, encoding: .utf8)
        }
    }
    
    func exportLogs() async throws -> URL {
        await createLogDirectoryIfNeeded()
        
        let exportFileName = "mcpcopilot_logs_\(dateFormatter.string(from: Date()).replacingOccurrences(of: ":", with: "-")).log"
        let exportURL = logFileURL.deletingLastPathComponent().appendingPathComponent(exportFileName)
        
        let logContent = logEntries.map { entry in
            "[\(entry.level.displayName)] \(dateFormatter.string(from: entry.timestamp)) \(entry.source ?? "Unknown"): \(entry.message)"
        }.joined(separator: "\n")
        
        try logContent.write(to: exportURL, atomically: true, encoding: .utf8)
        return exportURL
    }
    
    // MARK: - Private Methods
    private func createLogDirectoryIfNeeded() async {
        let logDirectory = logFileURL.deletingLastPathComponent()
        
        if !FileManager.default.fileExists(atPath: logDirectory.path) {
            try? FileManager.default.createDirectory(
                at: logDirectory,
                withIntermediateDirectories: true,
                attributes: nil
            )
        }
    }
    
    private func loadExistingLogs() async {
        guard FileManager.default.fileExists(atPath: logFileURL.path) else {
            return
        }
        
        do {
            let content = try String(contentsOf: logFileURL, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }
            
            // 解析最近的日志条目
            let recentLines = Array(lines.suffix(maxLogEntries))
            
            for line in recentLines {
                if let entry = parseLogLine(line) {
                    logEntries.append(entry)
                }
            }
        } catch {
            await log("Failed to load existing logs: \(error)", level: .error, source: "LoggingService")
        }
    }
    
    private func writeLogToFile(_ entry: LogEntry) async {
        let logLine = "[\(entry.level.displayName)] \(dateFormatter.string(from: entry.timestamp)) \(entry.source ?? "Unknown"): \(entry.message)\n"
        
        do {
            if FileManager.default.fileExists(atPath: logFileURL.path) {
                let fileHandle = try FileHandle(forWritingTo: logFileURL)
                fileHandle.seekToEndOfFile()
                fileHandle.write(logLine.data(using: .utf8) ?? Data())
                fileHandle.closeFile()
            } else {
                try logLine.write(to: logFileURL, atomically: true, encoding: .utf8)
            }
        } catch {
            print("Failed to write log to file: \(error)")
        }
    }
    
    private func parseLogLine(_ line: String) -> LogEntry? {
        // 简单的日志行解析
        // 格式: [LEVEL] TIMESTAMP SOURCE: MESSAGE
        let components = line.components(separatedBy: "] ")
        guard components.count >= 2 else { return nil }
        
        let levelString = String(components[0].dropFirst())
        let remainingPart = components[1]
        
        let level = LogLevel.allCases.first { $0.displayName == levelString } ?? .info
        
        let timestampAndMessage = remainingPart.components(separatedBy: " ")
        guard timestampAndMessage.count >= 3 else { return nil }
        
        let timestampString = "\(timestampAndMessage[0]) \(timestampAndMessage[1])"
        let timestamp = dateFormatter.date(from: timestampString) ?? Date()
        
        let sourceAndMessage = Array(timestampAndMessage.dropFirst(2)).joined(separator: " ")
        let sourceMessageComponents = sourceAndMessage.components(separatedBy: ": ")
        
        let source = sourceMessageComponents.first
        let message = sourceMessageComponents.count > 1 ? 
            Array(sourceMessageComponents.dropFirst()).joined(separator: ": ") : 
            sourceAndMessage
        
        return LogEntry(
            timestamp: timestamp,
            level: level,
            message: message,
            source: source
        )
    }
}
