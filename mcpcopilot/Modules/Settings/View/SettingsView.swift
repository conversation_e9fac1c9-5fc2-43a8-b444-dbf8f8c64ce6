//
//  SettingsView.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import SwiftUI

struct SettingsView: View {
    
    // MARK: - Properties
    @Environment(\.diContainer) private var diContainer
    @StateObject private var viewModel: SettingsViewModel
    
    // MARK: - Initialization
    init() {
        self._viewModel = StateObject(wrappedValue: DIContainer.shared.settingsViewModel)
    }
    
    // MARK: - Body
    var body: some View {
        NavigationView {
            Form {
                generalSection
                advancedSection
                aboutSection
            }
            .formStyle(.grouped)
            .navigationTitle("设置")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("重置") {
                        viewModel.resetToDefault()
                    }
                    .buttonStyle(.bordered)
                }
            }
            .alert("错误", isPresented: .constant(viewModel.errorMessage != nil)) {
                But<PERSON>("确定") {
                    viewModel.dismissMessages()
                }
            } message: {
                if let errorMessage = viewModel.errorMessage {
                    Text(errorMessage)
                }
            }
            .alert("成功", isPresented: .constant(viewModel.successMessage != nil)) {
                But<PERSON>("确定") {
                    viewModel.dismissMessages()
                }
            } message: {
                if let successMessage = viewModel.successMessage {
                    Text(successMessage)
                }
            }
            .disabled(viewModel.isLoading)
            .overlay {
                if viewModel.isLoading {
                    ProgressView("保存中...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.1))
                }
            }
        }
        .frame(minWidth: 600, minHeight: 500)
        .onAppear {
            viewModel.loadConfiguration()
        }
    }
    
    // MARK: - General Section
    private var generalSection: some View {
        Section("通用设置") {
            HStack {
                Text("默认端口")
                    .frame(width: 120, alignment: .leading)
                
                TextField("端口号", value: $viewModel.configuration.defaultPort, format: .number)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 100)
                
                if !viewModel.isValidPort {
                    Text("端口号必须在 1-65535 之间")
                        .foregroundColor(.statusError)
                        .font(.caption)
                }
            }
            
            HStack {
                Text("自动启动")
                    .frame(width: 120, alignment: .leading)
                
                Toggle("", isOn: $viewModel.configuration.autoStart)
                    .toggleStyle(.switch)
                
                Spacer()
                
                Text("应用启动时自动启动上次运行的场景")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            HStack {
                Text("系统托盘")
                    .frame(width: 120, alignment: .leading)
                
                Toggle("", isOn: $viewModel.configuration.enableSystemTray)
                    .toggleStyle(.switch)
                
                Spacer()
                
                Text("在系统托盘显示应用图标")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            HStack {
                Text("语言")
                    .frame(width: 120, alignment: .leading)
                
                Picker("语言", selection: $viewModel.configuration.language) {
                    ForEach(viewModel.availableLanguages, id: \.code) { language in
                        Text(language.name)
                            .tag(language.code)
                    }
                }
                .pickerStyle(.menu)
                .frame(width: 150)
                
                Spacer()
            }
        }
    }
    
    // MARK: - Advanced Section
    private var advancedSection: some View {
        Section("高级设置") {
            HStack {
                Text("日志级别")
                    .frame(width: 120, alignment: .leading)
                
                Picker("日志级别", selection: $viewModel.configuration.logLevel) {
                    ForEach(viewModel.availableLogLevels, id: \.self) { level in
                        Text(level.displayName)
                            .tag(level)
                    }
                }
                .pickerStyle(.menu)
                .frame(width: 100)
                
                Spacer()
                
                Text("设置应用的日志详细程度")
                    .font(.caption)
                    .foregroundColor(.textSecondary)
            }
            
            HStack {
                Text("最大重启次数")
                    .frame(width: 120, alignment: .leading)
                
                TextField("次数", value: $viewModel.configuration.maxRestartAttempts, format: .number)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 80)
                
                if !viewModel.isValidMaxRestartAttempts {
                    Text("重启次数必须在 0-10 之间")
                        .foregroundColor(.statusError)
                        .font(.caption)
                } else {
                    Text("进程异常退出时的最大自动重启次数")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
            
            HStack {
                Text("监控间隔")
                    .frame(width: 120, alignment: .leading)
                
                TextField("秒", value: $viewModel.configuration.monitoringInterval, format: .number)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 80)
                
                Text("秒")
                    .font(.caption)
                
                if !viewModel.isValidMonitoringInterval {
                    Text("监控间隔必须在 1-60 秒之间")
                        .foregroundColor(.statusError)
                        .font(.caption)
                } else {
                    Text("系统资源监控的更新间隔")
                        .font(.caption)
                        .foregroundColor(.textSecondary)
                }
            }
        }
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        Section("关于") {
            VStack(alignment: .leading, spacing: Spacing.md) {
                HStack {
                    Image(systemName: "network")
                        .font(.system(size: 32))
                        .foregroundColor(.primaryBlue)
                    
                    VStack(alignment: .leading, spacing: Spacing.xs) {
                        Text("MCP Copilot")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        Text("版本 1.0.0")
                            .font(.caption)
                            .foregroundColor(.textSecondary)
                    }
                    
                    Spacer()
                }
                
                Text("MCP 本地桥接客户端，用于管理多个 MCP Server 场景，并统一通过 HTTP/SSE 接口暴露给本地 AI 客户端。")
                    .font(.body)
                    .foregroundColor(.textSecondary)
                
                HStack(spacing: Spacing.md) {
                    Button("查看文档") {
                        // TODO: 打开文档链接
                    }
                    .buttonStyle(.bordered)
                    
                    Button("GitHub") {
                        // TODO: 打开 GitHub 链接
                    }
                    .buttonStyle(.bordered)
                    
                    Button("反馈问题") {
                        // TODO: 打开问题反馈链接
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding(.vertical, Spacing.sm)
        }
    }
}

// MARK: - Preview
#Preview {
    SettingsView()
        .environment(\.diContainer, DIContainer.shared)
}
