//
//  SettingsViewModel.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class SettingsViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var configuration: AppConfiguration = .default
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    // MARK: - Dependencies
    private let configurationRepository: ConfigurationRepositoryProtocol
    private let storageService: StorageServiceProtocol
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        configurationRepository: ConfigurationRepositoryProtocol,
        storageService: StorageServiceProtocol
    ) {
        self.configurationRepository = configurationRepository
        self.storageService = storageService
        
        setupBindings()
        loadConfiguration()
    }
    
    // MARK: - Public Methods
    func loadConfiguration() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                try await configurationRepository.loadConfiguration()
                await MainActor.run {
                    self.configuration = configurationRepository.appConfiguration
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "加载配置失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func saveConfiguration() {
        isLoading = true
        errorMessage = nil
        successMessage = nil
        
        Task {
            do {
                try await configurationRepository.saveConfiguration(configuration)
                await MainActor.run {
                    self.successMessage = "配置保存成功"
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "保存配置失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func resetToDefault() {
        Task {
            do {
                try await configurationRepository.resetToDefault()
                await MainActor.run {
                    self.configuration = configurationRepository.appConfiguration
                    self.successMessage = "配置已重置为默认值"
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "重置配置失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func updateDefaultPort(_ port: Int) {
        configuration.defaultPort = port
        saveConfiguration()
    }
    
    func updateAutoStart(_ autoStart: Bool) {
        configuration.autoStart = autoStart
        saveConfiguration()
    }
    
    func updateLogLevel(_ logLevel: LogLevel) {
        configuration.logLevel = logLevel
        saveConfiguration()
    }
    
    func updateMaxRestartAttempts(_ attempts: Int) {
        configuration.maxRestartAttempts = attempts
        saveConfiguration()
    }
    
    func updateMonitoringInterval(_ interval: TimeInterval) {
        configuration.monitoringInterval = interval
        saveConfiguration()
    }
    
    func updateSystemTrayEnabled(_ enabled: Bool) {
        configuration.enableSystemTray = enabled
        saveConfiguration()
    }
    
    func updateLanguage(_ language: String) {
        configuration.language = language
        saveConfiguration()
    }
    
    func dismissMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    // MARK: - Private Methods
    private func setupBindings() {
        configurationRepository.$appConfiguration
            .receive(on: DispatchQueue.main)
            .sink { [weak self] config in
                self?.configuration = config
            }
            .store(in: &cancellables)
    }
}

// MARK: - Validation Extensions
extension SettingsViewModel {
    
    var isValidPort: Bool {
        return configuration.defaultPort > 0 && configuration.defaultPort <= 65535
    }
    
    var isValidMaxRestartAttempts: Bool {
        return configuration.maxRestartAttempts >= 0 && configuration.maxRestartAttempts <= 10
    }
    
    var isValidMonitoringInterval: Bool {
        return configuration.monitoringInterval >= 1.0 && configuration.monitoringInterval <= 60.0
    }
    
    var canSave: Bool {
        return isValidPort && isValidMaxRestartAttempts && isValidMonitoringInterval
    }
}

// MARK: - Helper Extensions
extension SettingsViewModel {
    
    var availableLanguages: [(code: String, name: String)] {
        return [
            ("zh-Hans", "简体中文"),
            ("en", "English")
        ]
    }
    
    var availableLogLevels: [LogLevel] {
        return LogLevel.allCases
    }
    
    func displayName(for language: String) -> String {
        return availableLanguages.first { $0.code == language }?.name ?? language
    }
}
