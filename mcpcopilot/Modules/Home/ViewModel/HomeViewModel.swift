//
//  HomeViewModel.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class HomeViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var scenes: [MCPScene] = []
    @Published var selectedScene: MCPScene?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingAddScene = false
    @Published var showingEditScene = false
    
    // MARK: - Dependencies
    private let sceneRepository: SceneRepositoryProtocol
    private let processService: ProcessServiceProtocol
    private let monitoringService: MonitoringServiceProtocol
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init(
        sceneRepository: SceneRepositoryProtocol,
        processService: ProcessServiceProtocol,
        monitoringService: MonitoringServiceProtocol
    ) {
        self.sceneRepository = sceneRepository
        self.processService = processService
        self.monitoringService = monitoringService
        
        setupBindings()
        loadScenes()
    }
    
    // MARK: - Public Methods
    func loadScenes() {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                try await sceneRepository.loadScenes()
                await MainActor.run {
                    self.scenes = sceneRepository.scenes
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "加载场景失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func selectScene(_ scene: MCPScene) {
        selectedScene = scene
    }
    
    func startScene(_ scene: MCPScene) {
        Task {
            do {
                try await processService.startScene(scene)
                await updateSceneStatus(scene, isActive: true)
            } catch {
                await MainActor.run {
                    self.errorMessage = "启动场景失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func stopScene(_ scene: MCPScene) {
        Task {
            do {
                try await processService.stopScene(scene)
                await updateSceneStatus(scene, isActive: false)
            } catch {
                await MainActor.run {
                    self.errorMessage = "停止场景失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func restartScene(_ scene: MCPScene) {
        Task {
            do {
                try await processService.restartScene(scene)
            } catch {
                await MainActor.run {
                    self.errorMessage = "重启场景失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func deleteScene(_ scene: MCPScene) {
        Task {
            do {
                // 如果场景正在运行，先停止它
                if scene.isActive {
                    try await processService.stopScene(scene)
                }
                
                try await sceneRepository.deleteScene(scene)
                
                await MainActor.run {
                    self.scenes = sceneRepository.scenes
                    if self.selectedScene?.id == scene.id {
                        self.selectedScene = nil
                    }
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "删除场景失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func addNewScene() {
        showingAddScene = true
    }
    
    func editScene(_ scene: MCPScene) {
        selectedScene = scene
        showingEditScene = true
    }
    
    func saveScene(_ scene: MCPScene) {
        Task {
            do {
                try await sceneRepository.saveScene(scene)
                await MainActor.run {
                    self.scenes = sceneRepository.scenes
                    self.showingAddScene = false
                    self.showingEditScene = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "保存场景失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func getProcessStatus(for scene: MCPScene) -> ProcessStatus {
        return processService.getProcessStatus(for: scene.id)
    }
    
    func dismissError() {
        errorMessage = nil
    }
    
    // MARK: - Private Methods
    private func setupBindings() {
        // 监听场景仓库的变化
        sceneRepository.$scenes
            .receive(on: DispatchQueue.main)
            .sink { [weak self] scenes in
                self?.scenes = scenes
            }
            .store(in: &cancellables)
    }
    
    private func updateSceneStatus(_ scene: MCPScene, isActive: Bool) async {
        scene.isActive = isActive
        
        do {
            try await sceneRepository.saveScene(scene)
        } catch {
            await MainActor.run {
                self.errorMessage = "更新场景状态失败: \(error.localizedDescription)"
            }
        }
    }
}

// MARK: - Scene Management Extensions
extension HomeViewModel {
    
    var activeScenes: [MCPScene] {
        return scenes.filter { $0.isActive }
    }
    
    var inactiveScenes: [MCPScene] {
        return scenes.filter { !$0.isActive }
    }
    
    var hasActiveScenes: Bool {
        return !activeScenes.isEmpty
    }
    
    func canStartScene(_ scene: MCPScene) -> Bool {
        return !scene.isActive && getProcessStatus(for: scene) == .stopped
    }
    
    func canStopScene(_ scene: MCPScene) -> Bool {
        return scene.isActive && getProcessStatus(for: scene) == .running
    }
    
    func canRestartScene(_ scene: MCPScene) -> Bool {
        return scene.isActive
    }
}
