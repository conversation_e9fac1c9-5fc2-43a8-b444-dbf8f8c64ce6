//
//  SceneRepository.swift
//  mcpcopilot
//
//  Created by 张国豪 on 2025/7/14.
//

import Foundation
import Combine

@MainActor
final class SceneRepository: SceneRepositoryProtocol {
    
    // MARK: - Properties
    @Published private(set) var scenes: [MCPScene] = []
    
    private let storageService: StorageServiceProtocol
    private let configurationService: ConfigurationServiceProtocol
    private let storageKey = "scenes"
    
    // MARK: - Initialization
    init(storageService: StorageServiceProtocol, configurationService: ConfigurationServiceProtocol) {
        self.storageService = storageService
        self.configurationService = configurationService
        
        Task {
            await loadScenes()
        }
    }
    
    // MARK: - Scene Management
    func loadScenes() async throws {
        do {
            if let loadedScenes: [MCPSceneData] = try await storageService.load([MCPSceneData].self, from: storageKey) {
                scenes = loadedScenes.map { $0.toMCPScene() }
            } else {
                // 如果没有保存的场景，创建默认场景
                scenes = [createDefaultScene()]
                try await saveScenes()
            }
        } catch {
            // 如果加载失败，使用默认场景
            scenes = [createDefaultScene()]
            try await saveScenes()
            throw error
        }
    }
    
    func saveScene(_ scene: MCPScene) async throws {
        scene.updatedAt = Date()
        
        if let index = scenes.firstIndex(where: { $0.id == scene.id }) {
            scenes[index] = scene
        } else {
            scenes.append(scene)
        }
        
        try await saveScenes()
    }
    
    func deleteScene(_ scene: MCPScene) async throws {
        scenes.removeAll { $0.id == scene.id }
        try await saveScenes()
    }
    
    func getScene(by id: UUID) -> MCPScene? {
        return scenes.first { $0.id == id }
    }
    
    // MARK: - Private Methods
    private func saveScenes() async throws {
        let sceneData = scenes.map { MCPSceneData(from: $0) }
        try await storageService.save(sceneData, to: storageKey)
    }
    
    private func createDefaultScene() -> MCPScene {
        let configuration = configurationService.getDefaultConfiguration()
        
        return MCPScene(
            name: "默认场景",
            port: configuration.defaultPort,
            isActive: false,
            environment: [:]
        )
    }
}

// MARK: - Data Transfer Objects
private struct MCPSceneData: Codable {
    let id: UUID
    let name: String
    let port: Int
    let isActive: Bool
    let environment: [String: String]
    let createdAt: Date
    let updatedAt: Date
    let servers: [MCPServerData]
    
    init(from scene: MCPScene) {
        self.id = scene.id
        self.name = scene.name
        self.port = scene.port
        self.isActive = scene.isActive
        self.environment = scene.environment
        self.createdAt = scene.createdAt
        self.updatedAt = scene.updatedAt
        self.servers = scene.servers.map { MCPServerData(from: $0) }
    }
    
    func toMCPScene() -> MCPScene {
        let scene = MCPScene(
            id: id,
            name: name,
            port: port,
            isActive: isActive,
            environment: environment,
            createdAt: createdAt,
            updatedAt: updatedAt
        )
        
        scene.servers = servers.map { $0.toMCPServer(scene: scene) }
        return scene
    }
}

private struct MCPServerData: Codable {
    let id: UUID
    let name: String
    let namespace: String
    let type: MCPServerType
    let command: String
    let arguments: [String]
    let isRunning: Bool
    let pid: Int32?
    let startTime: Date?
    let restartCount: Int
    let createdAt: Date
    let updatedAt: Date
    
    init(from server: MCPServer) {
        self.id = server.id
        self.name = server.name
        self.namespace = server.namespace
        self.type = server.type
        self.command = server.command
        self.arguments = server.arguments
        self.isRunning = server.isRunning
        self.pid = server.pid
        self.startTime = server.startTime
        self.restartCount = server.restartCount
        self.createdAt = server.createdAt
        self.updatedAt = server.updatedAt
    }
    
    func toMCPServer(scene: MCPScene) -> MCPServer {
        let server = MCPServer(
            id: id,
            name: name,
            namespace: namespace,
            type: type,
            command: command,
            arguments: arguments,
            isRunning: isRunning,
            pid: pid,
            startTime: startTime,
            restartCount: restartCount,
            createdAt: createdAt,
            updatedAt: updatedAt
        )
        
        server.scene = scene
        return server
    }
}
