/* 
  Localizable.strings (English)
  mcpcopilot

  Created by 张国豪 on 2025/7/14.
*/

// MARK: - General
"OK" = "OK";
"Cancel" = "Cancel";
"Save" = "Save";
"Delete" = "Delete";
"Edit" = "Edit";
"Add" = "Add";
"Remove" = "Remove";
"Start" = "Start";
"Stop" = "Stop";
"Restart" = "Restart";
"Loading" = "Loading...";
"Error" = "Error";
"Warning" = "Warning";
"Info" = "Info";
"Success" = "Success";

// MARK: - App
"app.title" = "MCP Copilot";
"app.description" = "MCP Local Bridge Client";

// MARK: - Scene Management
"scene.title" = "Scenes";
"scene.create" = "Create Scene";
"scene.edit" = "Edit Scene";
"scene.delete" = "Delete Scene";
"scene.start" = "Start Scene";
"scene.stop" = "Stop Scene";
"scene.restart" = "Restart Scene";
"scene.name" = "Scene Name";
"scene.port" = "Port";
"scene.status" = "Status";
"scene.servers" = "Servers";
"scene.environment" = "Environment Variables";
"scene.created" = "Created";
"scene.updated" = "Updated";

// MARK: - Scene Status
"scene.status.running" = "Running";
"scene.status.stopped" = "Stopped";
"scene.status.starting" = "Starting";
"scene.status.stopping" = "Stopping";
"scene.status.error" = "Error";

// MARK: - Server Management
"server.title" = "MCP Servers";
"server.add" = "Add Server";
"server.edit" = "Edit Server";
"server.delete" = "Delete Server";
"server.name" = "Server Name";
"server.namespace" = "Namespace";
"server.type" = "Type";
"server.command" = "Command";
"server.arguments" = "Arguments";

// MARK: - Environment Variables
"env.title" = "Environment Variables";
"env.add" = "Add Environment Variable";
"env.key" = "Key";
"env.value" = "Value";
"env.empty" = "No environment variables";

// MARK: - Monitoring
"monitor.title" = "Monitoring";
"monitor.cpu" = "CPU Usage";
"monitor.memory" = "Memory Usage";
"monitor.uptime" = "Uptime";
"monitor.pid" = "Process ID";

// MARK: - Settings
"settings.title" = "Settings";
"settings.general" = "General";
"settings.advanced" = "Advanced";
"settings.about" = "About";
"settings.default_port" = "Default Port";
"settings.auto_start" = "Auto Start";
"settings.log_level" = "Log Level";
"settings.max_restart_attempts" = "Max Restart Attempts";
"settings.monitoring_interval" = "Monitoring Interval";
"settings.system_tray" = "System Tray";
"settings.language" = "Language";

// MARK: - Logs
"log.title" = "Logs";
"log.clear" = "Clear Logs";
"log.export" = "Export Logs";
"log.level.debug" = "Debug";
"log.level.info" = "Info";
"log.level.warning" = "Warning";
"log.level.error" = "Error";

// MARK: - Menu Bar
"menubar.open_main_window" = "Open Main Window";
"menubar.create_scene" = "Create New Scene";
"menubar.stop_all_scenes" = "Stop All Scenes";
"menubar.settings" = "Settings";
"menubar.view_logs" = "View Logs";
"menubar.about" = "About";
"menubar.quit" = "Quit";

// MARK: - Empty States
"empty.scenes" = "No scenes";
"empty.scenes.description" = "Click the + button above to create your first scene";
"empty.servers" = "No MCP servers";
"empty.servers.description" = "Click the + button above to add your first server";
"empty.environment" = "No environment variables";
"empty.environment.description" = "Click the edit button to add environment variables";

// MARK: - Welcome
"welcome.title" = "Welcome to MCP Copilot";
"welcome.description" = "Select a scene from the sidebar to view details, or create a new scene to get started.";
"welcome.create_scene" = "Create Scene";
"welcome.view_docs" = "View Documentation";

// MARK: - Errors
"error.load_scenes" = "Failed to load scenes";
"error.save_scene" = "Failed to save scene";
"error.delete_scene" = "Failed to delete scene";
"error.start_scene" = "Failed to start scene";
"error.stop_scene" = "Failed to stop scene";
"error.restart_scene" = "Failed to restart scene";
"error.invalid_input" = "Please check if the input information is correct";
"error.invalid_port" = "Port number must be a valid number";

// MARK: - Confirmations
"confirm.delete_scene" = "Are you sure you want to delete scene \"%@\"? This action cannot be undone.";
"confirm.stop_all_scenes" = "Are you sure you want to stop all running scenes?";

// MARK: - Tooltips
"tooltip.start_scene" = "Start scene";
"tooltip.stop_scene" = "Stop scene";
"tooltip.restart_scene" = "Restart scene";
"tooltip.more_options" = "More options";

// MARK: - Placeholders
"placeholder.scene_name" = "Enter scene name";
"placeholder.port_number" = "Enter port number";
"placeholder.env_key" = "Key";
"placeholder.env_value" = "Value";
